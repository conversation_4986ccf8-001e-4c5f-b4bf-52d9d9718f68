# 后台管理系统 (Admin Management System)

一个功能完整的后台管理系统，采用前后端分离架构。

## 技术栈

### 前端
- **框架**: React 18+ with TypeScript
- **UI库**: AWS Cloudscape Design System
- **构建工具**: Vite
- **状态管理**: React Query + Zustand
- **路由**: React Router v6
- **HTTP客户端**: Axios

### 后端
- **框架**: FastAPI (Python 3.11+)
- **数据库**: PostgreSQL 15+
- **ORM**: SQLAlchemy 2.0+
- **数据库驱动**: psycopg[binary]
- **认证**: JWT + OAuth2
- **API文档**: OpenAPI/Swagger
- **数据验证**: Pydantic v2

## 功能模块

### 系统管理模块
- [x] 用户管理 - 用户CRUD、状态管理、密码重置
- [x] 角色管理 - 角色定义、权限分配
- [x] 权限管理 - 细粒度权限控制
- [x] 菜单管理 - 动态菜单配置
- [x] API管理 - API权限控制
- [x] SSO集成 - 单点登录支持
- [x] 通知渠道管理 - 邮件、短信、推送通知
- [x] 系统操作日志 - 审计日志记录

### 扩展功能模块
- [x] 仪表板 - 数据统计和可视化
- [x] 文件管理 - 文件上传、下载、预览
- [x] 系统配置 - 全局配置管理
- [x] 数据字典 - 系统字典管理
- [x] 定时任务 - 任务调度管理
- [x] 系统监控 - 性能监控、健康检查
- [x] 备份恢复 - 数据备份和恢复
- [x] 多租户支持 - 租户隔离

## 项目结构

```
zengzhang-v2/
├── backend/                 # 后端项目
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模式
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── alembic/            # 数据库迁移
│   ├── tests/              # 测试文件
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端项目
│   ├── src/
│   │   ├── components/     # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── services/       # API服务
│   │   ├── stores/         # 状态管理
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
├── docker/                 # Docker配置
├── docs/                   # 项目文档
└── scripts/                # 部署脚本
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.11+
- PostgreSQL 15+
- Docker (可选)

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd zengzhang-v2
```

2. **后端设置**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **前端设置**
```bash
cd frontend
npm install
```

4. **数据库设置**
```bash
# 创建数据库
createdb admin_system

# 运行迁移
cd backend
alembic upgrade head
```

5. **启动服务**
```bash
# 后端 (端口: 8000)
cd backend
uvicorn app.main:app --reload

# 前端 (端口: 5173)
cd frontend
npm run dev
```

## 开发指南

### API文档
- 开发环境: http://localhost:8000/docs
- 生产环境: https://your-domain.com/docs

### 代码规范
- 前端: ESLint + Prettier
- 后端: Black + isort + mypy

### 测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm test
```

## 部署

支持多种部署方式：
- Docker Compose
- Kubernetes
- 传统服务器部署

详细部署文档请参考 `docs/deployment.md`

## 许可证

MIT License
